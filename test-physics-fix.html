<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PhysicsManager 修复验证测试</title>
    <style>
        body {
            font-family: '<PERSON>sol<PERSON>', 'Monaco', monospace;
            background-color: #1e1e1e;
            color: #d4d4d4;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            background-color: #2d2d30;
            border: 1px solid #3e3e42;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .test-title {
            color: #569cd6;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .test-result {
            background-color: #1e1e1e;
            border: 1px solid #3e3e42;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Consolas', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .success {
            color: #4ec9b0;
        }
        .error {
            color: #f44747;
        }
        .warning {
            color: #ffcc02;
        }
        .info {
            color: #9cdcfe;
        }
        button {
            background-color: #0e639c;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        button:hover {
            background-color: #1177bb;
        }
        button:disabled {
            background-color: #3e3e42;
            cursor: not-allowed;
        }
        #canvas {
            border: 1px solid #3e3e42;
            background-color: #000;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 PhysicsManager 修复验证测试</h1>
        
        <div class="test-section">
            <div class="test-title">📋 修复内容说明</div>
            <div class="test-result">
<span class="info">本次修复解决了以下问题：</span>

<span class="success">1. ✅ 修复了 engine.setMaxSubSteps 方法不存在的错误</span>
   - 移除了不存在的 setMaxSubSteps 方法调用
   - 添加了安全检查和错误处理
   - 保留了 setSubTimeStep 方法（如果存在）

<span class="success">2. ✅ 修复了 ESLint "Unexpected lexical declaration in case block" 错误</span>
   - 为所有 switch case 块添加了大括号 {}
   - 解决了变量声明作用域问题
   - 涉及第 200, 205, 210, 228 行的修复

<span class="success">3. ✅ 修复了其他代码质量问题</span>
   - 将弃用的 substr() 方法替换为 substring()
   - 修复了未使用的 deltaTime 参数警告
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🧪 基础功能测试</div>
            <button onclick="testBasicFunctionality()">运行基础测试</button>
            <div id="basicTest" class="test-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">🎯 物理引擎初始化测试</div>
            <button onclick="testPhysicsInitialization()">测试物理引擎初始化</button>
            <div id="physicsTest" class="test-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">🎮 Babylon.js 场景测试</div>
            <button onclick="testBabylonScene()">创建测试场景</button>
            <canvas id="canvas" width="800" height="400"></canvas>
            <div id="sceneTest" class="test-result"></div>
        </div>
    </div>

    <script type="module">
        // 导入必要的模块
        import { Engine, Scene, Vector3, FreeCamera, HemisphericLight, MeshBuilder } from '@babylonjs/core';
        import { PhysicsManager, PhysicsShapeTypes, PhysicsMaterialPresets } from './src/core/physics/PhysicsManager.js';

        // 全局变量
        let engine, scene, physicsManager;

        // 基础功能测试
        window.testBasicFunctionality = function() {
            const output = document.getElementById('basicTest');
            output.innerHTML = '<span class="info">🔄 正在运行基础功能测试...</span>\n';

            try {
                // 创建基础引擎和场景
                const canvas = document.getElementById('canvas');
                engine = new Engine(canvas, true);
                scene = new Scene(engine);

                // 创建物理管理器
                physicsManager = new PhysicsManager(scene);

                let result = '';
                result += '<span class="success">✅ PhysicsManager 创建成功</span>\n';
                result += `<span class="info">初始化状态: ${physicsManager.isReady()}</span>\n`;
                
                // 测试配置
                const gravity = physicsManager.getGravity();
                result += `<span class="info">默认重力: ${gravity.toString()}</span>\n`;
                
                // 测试统计信息
                const stats = physicsManager.getStats();
                result += `<span class="info">统计信息: ${JSON.stringify(stats, null, 2)}</span>\n`;
                
                // 测试形状类型
                result += `<span class="info">支持的形状类型: ${Object.values(PhysicsShapeTypes).join(', ')}</span>\n`;
                
                // 测试材质预设
                result += `<span class="info">材质预设: ${Object.keys(PhysicsMaterialPresets).join(', ')}</span>\n`;
                
                // 测试ID生成器
                const bodyId = physicsManager.generateBodyId();
                const triggerId = physicsManager.generateTriggerId();
                result += `<span class="info">生成的物理体ID: ${bodyId}</span>\n`;
                result += `<span class="info">生成的触发器ID: ${triggerId}</span>\n`;
                
                result += '\n<span class="success">🎉 基础功能测试全部通过！</span>';
                output.innerHTML = result;

            } catch (error) {
                output.innerHTML = `<span class="error">❌ 基础功能测试失败: ${error.message}</span>\n<span class="error">${error.stack}</span>`;
            }
        };

        // 物理引擎初始化测试
        window.testPhysicsInitialization = async function() {
            const output = document.getElementById('physicsTest');
            output.innerHTML = '<span class="info">🔄 正在测试物理引擎初始化...</span>\n';

            try {
                if (!physicsManager) {
                    throw new Error('请先运行基础功能测试');
                }

                let result = '';
                result += '<span class="info">开始初始化 Havok 物理引擎...</span>\n';

                // 尝试初始化物理引擎
                const initSuccess = await physicsManager.initialize();
                
                if (initSuccess) {
                    result += '<span class="success">✅ 物理引擎初始化成功！</span>\n';
                    result += `<span class="info">引擎就绪状态: ${physicsManager.isReady()}</span>\n`;
                    
                    // 测试设置重力
                    const newGravity = new Vector3(0, -20, 0);
                    physicsManager.setGravity(newGravity);
                    result += `<span class="info">重力设置测试: ${physicsManager.getGravity().toString()}</span>\n`;
                    
                    // 测试创建物理形状（现在应该成功）
                    const boxShape = physicsManager.createPhysicsShape(PhysicsShapeTypes.BOX, {
                        size: new Vector3(2, 2, 2)
                    });
                    result += `<span class="success">✅ 物理形状创建测试: ${boxShape ? '成功' : '失败'}</span>\n`;
                    
                } else {
                    result += '<span class="warning">⚠️ 物理引擎初始化失败（这在某些环境下是正常的）</span>\n';
                    result += '<span class="info">可能原因：Havok 物理引擎需要 WebAssembly 支持</span>\n';
                }

                result += '\n<span class="success">🎉 物理引擎初始化测试完成！</span>';
                output.innerHTML = result;

            } catch (error) {
                output.innerHTML = `<span class="error">❌ 物理引擎初始化测试失败: ${error.message}</span>\n<span class="error">${error.stack}</span>`;
            }
        };

        // Babylon.js 场景测试
        window.testBabylonScene = function() {
            const output = document.getElementById('sceneTest');
            output.innerHTML = '<span class="info">🔄 正在创建 Babylon.js 测试场景...</span>\n';

            try {
                if (!engine || !scene) {
                    throw new Error('请先运行基础功能测试');
                }

                let result = '';

                // 创建相机
                const camera = new FreeCamera("camera", new Vector3(0, 5, -10), scene);
                camera.setTarget(Vector3.Zero());
                camera.attachToCanvas(document.getElementById('canvas'), true);

                // 创建光源
                const light = new HemisphericLight("light", new Vector3(0, 1, 0), scene);
                light.intensity = 0.7;

                // 创建地面
                const ground = MeshBuilder.CreateGround("ground", {width: 10, height: 10}, scene);
                
                // 创建一个盒子
                const box = MeshBuilder.CreateBox("box", {size: 2}, scene);
                box.position.y = 1;

                result += '<span class="success">✅ 相机创建成功</span>\n';
                result += '<span class="success">✅ 光源创建成功</span>\n';
                result += '<span class="success">✅ 地面创建成功</span>\n';
                result += '<span class="success">✅ 盒子创建成功</span>\n';

                // 开始渲染循环
                engine.runRenderLoop(() => {
                    scene.render();
                });

                // 处理窗口大小变化
                window.addEventListener("resize", () => {
                    engine.resize();
                });

                result += '\n<span class="success">🎉 Babylon.js 场景创建成功！</span>\n';
                result += '<span class="info">场景正在上方的画布中渲染</span>';
                output.innerHTML = result;

            } catch (error) {
                output.innerHTML = `<span class="error">❌ Babylon.js 场景测试失败: ${error.message}</span>\n<span class="error">${error.stack}</span>`;
            }
        };

        // 页面加载完成后的提示
        console.log('🚀 PhysicsManager 修复验证测试页面已加载');
        console.log('请点击相应按钮运行测试');
    </script>
</body>
</html>
