<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>输入系统调试 - Cantos MMORPG</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .debug-panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .debug-button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            border: none;
            border-radius: 8px;
            padding: 15px 20px;
            color: white;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .debug-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
        }
        
        .debug-button.secondary {
            background: linear-gradient(45deg, #2196F3, #1976D2);
        }
        
        .debug-button.warning {
            background: linear-gradient(45deg, #FF9800, #F57C00);
        }
        
        .debug-button.danger {
            background: linear-gradient(45deg, #F44336, #D32F2F);
        }
        
        .console-output {
            background: rgba(0, 0, 0, 0.7);
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            height: 300px;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.1);
            white-space: pre-wrap;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-indicator.online {
            background: #4CAF50;
            box-shadow: 0 0 8px rgba(76, 175, 80, 0.6);
        }
        
        .status-indicator.offline {
            background: #F44336;
            box-shadow: 0 0 8px rgba(244, 67, 54, 0.6);
        }
        
        .status-indicator.warning {
            background: #FF9800;
            box-shadow: 0 0 8px rgba(255, 152, 0, 0.6);
        }
        
        .info-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .info-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #ffd700;
        }
        
        .iframe-container {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 10px;
            margin-top: 20px;
        }
        
        .main-game-frame {
            width: 100%;
            height: 400px;
            border: none;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Cantos MMORPG 输入系统调试工具</h1>
            <p>测试和调试新的输入管理器系统</p>
        </div>
        
        <div class="debug-panel">
            <div class="info-title">🎮 系统状态</div>
            <div class="info-card">
                <div id="system-status">
                    <div><span class="status-indicator offline"></span>输入管理器: 检查中...</div>
                    <div><span class="status-indicator offline"></span>输入配置: 检查中...</div>
                    <div><span class="status-indicator offline"></span>主游戏: 检查中...</div>
                </div>
            </div>
        </div>
        
        <div class="debug-panel">
            <div class="info-title">🛠️ 调试工具</div>
            <div class="button-grid">
                <button class="debug-button" onclick="checkSystemStatus()">
                    🔍 检查系统状态
                </button>
                <button class="debug-button secondary" onclick="testInputEvents()">
                    🎯 测试输入事件
                </button>
                <button class="debug-button secondary" onclick="testInputMappings()">
                    🗺️ 测试输入映射
                </button>
                <button class="debug-button secondary" onclick="testConfigManagement()">
                    ⚙️ 测试配置管理
                </button>
                <button class="debug-button warning" onclick="runFullTest()">
                    🚀 运行完整测试
                </button>
                <button class="debug-button danger" onclick="cleanupAllTests()">
                    🧹 清理所有测试
                </button>
            </div>
        </div>
        
        <div class="debug-panel">
            <div class="info-title">📝 控制台输出</div>
            <div class="console-output" id="console-output">
                等待调试命令...
            </div>
        </div>
        
        <div class="debug-panel">
            <div class="info-title">🎮 主游戏窗口</div>
            <div class="iframe-container">
                <iframe 
                    src="/" 
                    class="main-game-frame" 
                    id="game-frame"
                    onload="onGameFrameLoad()">
                </iframe>
            </div>
        </div>
        
        <div class="debug-panel">
            <div class="info-title">📖 使用说明</div>
            <div class="info-card">
                <ul>
                    <li><strong>检查系统状态</strong>: 验证输入系统是否正确加载</li>
                    <li><strong>测试输入事件</strong>: 监听并显示输入事件</li>
                    <li><strong>测试输入映射</strong>: 验证键位映射功能</li>
                    <li><strong>测试配置管理</strong>: 测试配置保存和加载</li>
                    <li><strong>运行完整测试</strong>: 执行所有测试项目</li>
                    <li><strong>清理所有测试</strong>: 移除测试监听器</li>
                </ul>
                <p><strong>注意</strong>: 确保主游戏已加载完成再进行测试</p>
            </div>
        </div>
    </div>
    
    <script>
        // 控制台输出重定向
        const consoleOutput = document.getElementById('console-output');
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : '📝';
            const line = `[${timestamp}] ${prefix} ${message}\n`;
            
            consoleOutput.textContent += line;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };
        
        // 系统状态检查
        function updateSystemStatus() {
            const gameFrame = document.getElementById('game-frame');
            const gameWindow = gameFrame.contentWindow;
            
            const statusElements = document.querySelectorAll('#system-status div');
            
            try {
                // 检查输入管理器
                if (gameWindow.inputManager) {
                    statusElements[0].innerHTML = '<span class="status-indicator online"></span>输入管理器: ✅ 已加载';
                } else {
                    statusElements[0].innerHTML = '<span class="status-indicator offline"></span>输入管理器: ❌ 未找到';
                }
                
                // 检查输入配置
                if (gameWindow.inputConfig) {
                    statusElements[1].innerHTML = '<span class="status-indicator online"></span>输入配置: ✅ 已加载';
                } else {
                    statusElements[1].innerHTML = '<span class="status-indicator offline"></span>输入配置: ❌ 未找到';
                }
                
                // 检查主游戏
                if (gameWindow.engine && gameWindow.sceneManager) {
                    statusElements[2].innerHTML = '<span class="status-indicator online"></span>主游戏: ✅ 运行中';
                } else {
                    statusElements[2].innerHTML = '<span class="status-indicator warning"></span>主游戏: ⚠️ 加载中';
                }
                
            } catch (error) {
                console.error('无法访问游戏窗口:', error);
                statusElements[0].innerHTML = '<span class="status-indicator offline"></span>输入管理器: ❌ 无法访问';
                statusElements[1].innerHTML = '<span class="status-indicator offline"></span>输入配置: ❌ 无法访问';
                statusElements[2].innerHTML = '<span class="status-indicator offline"></span>主游戏: ❌ 无法访问';
            }
        }
        
        // 游戏框架加载完成
        function onGameFrameLoad() {
            console.log('🎮 主游戏框架已加载');
            setTimeout(updateSystemStatus, 2000); // 等待游戏初始化
            setInterval(updateSystemStatus, 5000); // 定期更新状态
        }
        
        // 调试函数
        function checkSystemStatus() {
            console.log('🔍 检查系统状态...');
            updateSystemStatus();
            
            const gameFrame = document.getElementById('game-frame');
            const gameWindow = gameFrame.contentWindow;
            
            if (gameWindow.checkInputSystem) {
                gameWindow.checkInputSystem();
            } else {
                console.error('❌ 调试函数未找到，请确保主游戏已完全加载');
            }
        }
        
        function testInputEvents() {
            console.log('🎯 开始测试输入事件...');
            const gameFrame = document.getElementById('game-frame');
            const gameWindow = gameFrame.contentWindow;
            
            if (gameWindow.testInputEvents) {
                window.eventCleanup = gameWindow.testInputEvents();
            } else {
                console.error('❌ 测试函数未找到');
            }
        }
        
        function testInputMappings() {
            console.log('🗺️ 开始测试输入映射...');
            const gameFrame = document.getElementById('game-frame');
            const gameWindow = gameFrame.contentWindow;
            
            if (gameWindow.testInputMappings) {
                window.mappingCleanup = gameWindow.testInputMappings();
            } else {
                console.error('❌ 测试函数未找到');
            }
        }
        
        function testConfigManagement() {
            console.log('⚙️ 开始测试配置管理...');
            const gameFrame = document.getElementById('game-frame');
            const gameWindow = gameFrame.contentWindow;
            
            if (gameWindow.testConfigManagement) {
                gameWindow.testConfigManagement();
            } else {
                console.error('❌ 测试函数未找到');
            }
        }
        
        function runFullTest() {
            console.log('🚀 运行完整测试套件...');
            const gameFrame = document.getElementById('game-frame');
            const gameWindow = gameFrame.contentWindow;
            
            if (gameWindow.runFullTest) {
                gameWindow.runFullTest();
            } else {
                console.error('❌ 测试函数未找到');
            }
        }
        
        function cleanupAllTests() {
            console.log('🧹 清理所有测试...');
            
            if (window.eventCleanup) {
                window.eventCleanup();
                window.eventCleanup = null;
            }
            
            if (window.mappingCleanup) {
                window.mappingCleanup();
                window.mappingCleanup = null;
            }
            
            const gameFrame = document.getElementById('game-frame');
            const gameWindow = gameFrame.contentWindow;
            
            if (gameWindow.cleanupTest) {
                gameWindow.cleanupTest();
            }
            
            console.log('✅ 所有测试已清理');
        }
        
        // 初始化
        console.log('🔧 输入系统调试工具已加载');
        console.log('📝 等待主游戏加载完成...');
    </script>
</body>
</html>
