/**
 * 输入系统调试脚本
 * 在浏览器控制台中运行以测试输入系统
 */

// 检查输入系统是否已加载
function checkInputSystem() {
    console.log('🔍 检查输入系统状态...');
    
    const results = {
        inputManager: !!window.inputManager,
        inputConfig: !!window.inputConfig,
        inputManagerInitialized: window.inputManager ? window.inputManager.initialized : false,
        inputManagerEnabled: window.inputManager ? window.inputManager.enabled : false
    };
    
    console.log('📊 输入系统状态:', results);
    
    if (window.inputManager) {
        const stats = window.inputManager.getStats();
        console.log('📈 输入管理器统计:', stats);
    }
    
    if (window.inputConfig) {
        const configStats = window.inputConfig.getStats();
        console.log('⚙️ 输入配置统计:', configStats);
    }
    
    return results;
}

// 测试输入事件
function testInputEvents() {
    if (!window.inputManager) {
        console.error('❌ 输入管理器未找到');
        return;
    }
    
    console.log('🎮 开始测试输入事件...');
    
    // 添加测试事件监听器
    const testEvents = [
        'movement:start',
        'movement:update', 
        'movement:stop',
        'action:jump',
        'action:attack',
        'action:interact',
        'camera:rotate',
        'camera:zoom',
        'ui:menu',
        'ui:inventory'
    ];
    
    const listeners = {};
    
    testEvents.forEach(eventType => {
        const listener = (data) => {
            console.log(`🎯 事件触发: ${eventType}`, data);
        };
        
        window.inputManager.on(eventType, listener);
        listeners[eventType] = listener;
    });
    
    console.log('✅ 测试事件监听器已设置');
    console.log('🎮 现在可以按键测试输入事件');
    console.log('📝 支持的测试按键:');
    console.log('  - WASD: 移动');
    console.log('  - 空格: 跳跃');
    console.log('  - F: 攻击');
    console.log('  - E: 交互');
    console.log('  - ESC: 菜单');
    console.log('  - I: 背包');
    console.log('  - 鼠标移动: 相机旋转');
    console.log('  - 鼠标滚轮: 相机缩放');
    
    // 返回清理函数
    return () => {
        console.log('🧹 清理测试事件监听器...');
        testEvents.forEach(eventType => {
            if (listeners[eventType]) {
                window.inputManager.off(eventType, listeners[eventType]);
            }
        });
        console.log('✅ 测试事件监听器已清理');
    };
}

// 测试输入映射
function testInputMappings() {
    if (!window.inputManager) {
        console.error('❌ 输入管理器未找到');
        return;
    }
    
    console.log('🗺️ 测试输入映射...');
    
    const mappings = window.inputManager.getInputMappings();
    console.log('📋 当前输入映射:', mappings);
    
    // 测试添加自定义映射
    console.log('➕ 添加测试映射...');
    window.inputManager.setInputMapping('t', 'ui:chat', { test: true });
    
    // 验证映射是否添加成功
    const updatedMappings = window.inputManager.getInputMappings();
    const hasTestMapping = updatedMappings.has('t');
    
    console.log(`✅ 测试映射添加${hasTestMapping ? '成功' : '失败'}`);
    
    if (hasTestMapping) {
        console.log('🎮 按 T 键测试自定义映射');
        
        // 添加测试监听器
        const testListener = (data) => {
            console.log('🎯 自定义映射触发:', data);
        };
        
        window.inputManager.on('ui:chat', testListener);
        
        // 返回清理函数
        return () => {
            window.inputManager.off('ui:chat', testListener);
            window.inputManager.removeInputMapping('t');
            console.log('🧹 测试映射已清理');
        };
    }
}

// 测试配置管理
function testConfigManagement() {
    if (!window.inputConfig) {
        console.error('❌ 输入配置未找到');
        return;
    }
    
    console.log('⚙️ 测试配置管理...');
    
    // 获取当前配置
    const currentConfig = window.inputConfig.getConfig();
    console.log('📋 当前配置:', currentConfig);
    
    // 测试敏感度修改
    const originalSensitivity = window.inputConfig.getSensitivity().mouseSensitivity;
    console.log(`🖱️ 原始鼠标敏感度: ${originalSensitivity}`);
    
    window.inputConfig.setSensitivity('mouseSensitivity', 2.0);
    const newSensitivity = window.inputConfig.getSensitivity().mouseSensitivity;
    console.log(`🖱️ 新鼠标敏感度: ${newSensitivity}`);
    
    // 测试配置验证
    const validation = window.inputConfig.validateConfig();
    console.log('✅ 配置验证结果:', validation);
    
    // 测试配置导出
    const exportedConfig = window.inputConfig.exportToJSON();
    console.log('📤 导出配置长度:', exportedConfig.length, '字符');
    
    // 恢复原始敏感度
    window.inputConfig.setSensitivity('mouseSensitivity', originalSensitivity);
    console.log(`🔄 已恢复原始敏感度: ${originalSensitivity}`);
}

// 运行完整测试套件
function runFullTest() {
    console.log('🚀 开始完整输入系统测试...');
    console.log('='.repeat(50));
    
    // 1. 检查系统状态
    const systemStatus = checkInputSystem();
    
    if (!systemStatus.inputManager) {
        console.error('❌ 输入管理器未找到，测试终止');
        return;
    }
    
    // 2. 测试配置管理
    if (systemStatus.inputConfig) {
        testConfigManagement();
    }
    
    // 3. 测试输入映射
    const mappingCleanup = testInputMappings();
    
    // 4. 测试输入事件
    const eventCleanup = testInputEvents();
    
    console.log('='.repeat(50));
    console.log('✅ 完整测试设置完成');
    console.log('🎮 现在可以按键测试各种输入功能');
    console.log('🧹 运行 cleanupTest() 清理测试监听器');
    
    // 暴露清理函数到全局
    window.cleanupTest = () => {
        if (eventCleanup) eventCleanup();
        if (mappingCleanup) mappingCleanup();
        console.log('🧹 所有测试已清理完成');
    };
}

// 暴露函数到全局作用域
window.checkInputSystem = checkInputSystem;
window.testInputEvents = testInputEvents;
window.testInputMappings = testInputMappings;
window.testConfigManagement = testConfigManagement;
window.runFullTest = runFullTest;

console.log('🎮 输入系统调试脚本已加载');
console.log('📝 可用命令:');
console.log('  - checkInputSystem() - 检查系统状态');
console.log('  - testInputEvents() - 测试输入事件');
console.log('  - testInputMappings() - 测试输入映射');
console.log('  - testConfigManagement() - 测试配置管理');
console.log('  - runFullTest() - 运行完整测试');
console.log('🚀 运行 runFullTest() 开始完整测试');
